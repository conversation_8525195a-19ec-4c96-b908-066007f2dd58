<execution>
  <constraint>
    ## 文档驱动开发的客观限制
    - **时间投入约束**：文档编写需要额外时间投入，影响开发进度
    - **维护成本**：文档需要与代码同步更新，增加维护负担
    - **团队接受度**：部分开发者可能抗拒文档编写工作
    - **工具链集成**：文档工具与开发工具的集成复杂度
  </constraint>

  <rule>
    ## 文档驱动开发强制规则
    - **文档先行原则**：重要功能和架构决策必须先有文档再开发
    - **同步更新规则**：代码变更必须同步更新相关文档
    - **审查门禁**：文档质量作为代码审查的必要条件
    - **版本控制**：文档与代码使用相同的版本控制系统
    - **可执行文档**：API文档必须能够直接执行和测试
  </rule>

  <guideline>
    ## 文档编写指导原则
    - **用户视角**：从使用者角度编写文档，而非开发者视角
    - **结构化表达**：使用统一的文档模板和结构
    - **图文并茂**：适当使用图表、流程图增强理解
    - **示例驱动**：提供具体的代码示例和使用场景
    - **持续改进**：基于用户反馈持续优化文档质量
  </guideline>

  <process>
    ## 文档驱动开发流程
    
    ### Step 1: 需求文档化 (需求阶段)
    ```mermaid
    flowchart TD
        A[需求收集] --> B[用户故事编写]
        B --> C[验收标准定义]
        C --> D[原型设计]
        D --> E[技术方案文档]
        E --> F[开发任务分解]
    ```
    
    **文档产出**：
    - **需求规格说明书**：详细的功能需求和非功能需求
    - **用户故事文档**：以用户视角描述的功能场景
    - **原型设计文档**：交互原型和视觉设计说明
    - **技术方案文档**：架构设计和技术选型说明
    
    ### Step 2: 设计文档化 (设计阶段)
    ```mermaid
    graph TD
        A[系统架构文档] --> B[数据库设计文档]
        B --> C[API接口文档]
        C --> D[前端组件文档]
        D --> E[部署架构文档]
    ```
    
    **文档产出**：
    - **系统架构文档**：整体架构图和模块划分
    - **数据库设计文档**：ER图、表结构、索引设计
    - **API接口文档**：接口规范、参数说明、示例代码
    - **前端设计文档**：组件库、样式指南、交互规范
    
    ### Step 3: 开发文档化 (开发阶段)
    ```mermaid
    flowchart TD
        A[代码注释规范] --> B[开发日志记录]
        B --> C[测试用例文档]
        C --> D[部署脚本文档]
        D --> E[问题解决文档]
    ```
    
    **文档产出**：
    - **代码注释**：函数、类、模块的详细注释
    - **开发日志**：重要决策和问题解决过程记录
    - **测试文档**：测试用例、测试数据、测试报告
    - **部署文档**：环境配置、部署步骤、回滚方案
    
    ### Step 4: 交付文档化 (交付阶段)
    ```mermaid
    graph LR
        A[用户手册] --> B[运维手册]
        B --> C[故障排查手册]
        C --> D[版本发布说明]
        D --> E[项目总结文档]
    ```
    
    **文档产出**：
    - **用户手册**：功能使用说明、常见问题解答
    - **运维手册**：系统监控、备份恢复、性能调优
    - **故障排查手册**：常见问题诊断和解决方案
    - **发布说明**：版本更新内容、升级指南
    
    ## 📚 文档分类与管理
    
    ```mermaid
    mindmap
      root((项目文档))
        需求文档
          PRD产品需求
          用户故事
          验收标准
        设计文档
          架构设计
          接口设计
          数据库设计
        开发文档
          代码规范
          开发指南
          测试文档
        运维文档
          部署指南
          监控告警
          故障处理
    ```
    
    ## 🔄 文档生命周期管理
    
    ```mermaid
    graph TD
        A[文档创建] --> B[内容编写]
        B --> C[团队评审]
        C --> D{评审通过?}
        D -->|是| E[发布使用]
        D -->|否| F[修改完善]
        F --> C
        E --> G[使用反馈]
        G --> H[定期更新]
        H --> I{需要更新?}
        I -->|是| J[版本更新]
        I -->|否| K[继续使用]
        J --> B
        K --> G
    ```
    
    ## 📊 文档质量评估
    
    ### 内容质量维度
    - **准确性**：信息是否准确无误
    - **完整性**：是否覆盖所有必要信息
    - **清晰性**：表达是否清晰易懂
    - **实用性**：是否对读者有实际帮助
    
    ### 维护质量维度
    - **时效性**：是否及时更新
    - **一致性**：与代码是否保持一致
    - **可访问性**：是否易于查找和访问
    - **版本管理**：是否有清晰的版本历史
  </process>

  <criteria>
    ## 文档驱动开发质量标准
    
    ### 文档覆盖率指标
    - ✅ 核心功能文档覆盖率 100%
    - ✅ API接口文档覆盖率 100%
    - ✅ 重要决策文档化率 ≥ 90%
    - ✅ 故障处理文档完整性 ≥ 95%
    
    ### 文档质量指标
    - ✅ 文档准确性 ≥ 95%
    - ✅ 文档时效性（更新及时率）≥ 90%
    - ✅ 用户满意度 ≥ 85%
    - ✅ 文档查找效率 ≤ 2分钟
    
    ### 开发效率指标
    - ✅ 新人上手时间缩短 ≥ 50%
    - ✅ 问题解决效率提升 ≥ 30%
    - ✅ 代码审查效率提升 ≥ 25%
    - ✅ 知识传承效果 ≥ 80%
  </criteria>
</execution>
