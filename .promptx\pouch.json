{"currentState": "memory_saved", "stateHistory": [{"from": "initial", "command": "init", "timestamp": "2025-07-27T18:58:36.836Z", "args": [{"workingDirectory": "c:\\Users\\<USER>\\Desktop\\小说工具2", "ideType": "cursor"}]}, {"from": "initialized", "command": "welcome", "timestamp": "2025-07-27T18:58:40.856Z", "args": []}, {"from": "service_discovery", "command": "action", "timestamp": "2025-07-27T18:58:48.092Z", "args": ["fullstack-master"]}, {"from": "role_activated_with_memory", "command": "remember", "timestamp": "2025-07-27T19:00:54.650Z", "args": ["fullstack-master", "用户核心开发指令：\n1. 仔细认真查看提供的文档资料以及信息\n2. 严格认真按照提供的开发文档以及文档资料信息进行项目的开发\n3. 在开发过程中所有的测试文件、脚本、页面等，都在测试后都要清理干净\n4. 禁止创建多个文件、脚本、页面等，如需要修改、优化、完善等，都要在原有的文件、脚本、页面上进行\n5. 在创建界面UI风格，全部功能界面布局，图标，主题以及统一的组件、控件、按钮等，要按照开发文档里的来进行\n6. 每一个开发阶段完成后，都要进行测试，才能进入下一个阶段，如果测试不满意不合格就要一直测试\n7. 在开发过程中，一定要按照开发文档进行，特别的界面UI设计，界面布局，主题风格，色彩系统，图标以及统一的组件、控件、按钮等，开发文档里都有详细的规范说明", "--tags", "核心指令 开发规范 文档驱动 测试要求 UI规范"]}], "lastUpdated": "2025-07-27T19:00:54.670Z"}