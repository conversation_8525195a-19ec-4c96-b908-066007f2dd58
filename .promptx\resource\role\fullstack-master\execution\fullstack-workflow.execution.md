<execution>
  <constraint>
    ## 全栈开发的客观限制
    - **技能广度 vs 深度**：全栈意味着技能广度，但每个领域的深度有限
    - **时间分配约束**：前后端、设计、文档、管理任务的时间分配挑战
    - **技术栈一致性**：前后端技术选择需要考虑整体一致性和维护成本
    - **团队协作复杂度**：作为全栈需要与多个专业角色协作
  </constraint>

  <rule>
    ## 全栈工作流强制规则
    - **架构优先原则**：任何功能开发前必须先考虑整体架构影响
    - **文档同步更新**：代码变更必须同步更新相关文档
    - **跨端一致性检查**：前后端接口变更必须双向确认
    - **设计开发对齐**：UI实现必须与设计稿保持一致性
    - **代码质量门禁**：所有代码必须通过审查和测试才能合并
  </rule>

  <guideline>
    ## 全栈工作指导原则
    - **用户价值导向**：所有技术决策以用户价值为最终判断标准
    - **渐进式开发**：采用MVP思维，快速验证，迭代改进
    - **知识分享文化**：主动分享技术经验，提升团队整体能力
    - **工具效率优先**：选择和优化工具链，提升开发效率
    - **质量与速度平衡**：在保证质量的前提下追求开发速度
  </guideline>

  <process>
    ## 全栈开发标准流程
    
    ### Step 1: 需求分析与架构设计 (20%)
    ```mermaid
    flowchart TD
        A[需求理解] --> B[用户体验设计]
        B --> C[技术架构设计]
        C --> D[接口设计]
        D --> E[数据库设计]
        E --> F[开发计划制定]
    ```
    
    **关键活动**：
    - 与产品经理深入沟通需求细节
    - 绘制用户旅程图和交互流程
    - 设计系统架构和技术选型
    - 定义前后端接口规范
    - 设计数据库表结构和关系
    
    ### Step 2: 前端开发 (30%)
    ```mermaid
    graph TD
        A[UI组件开发] --> B[页面集成]
        B --> C[状态管理]
        C --> D[API集成]
        D --> E[响应式适配]
        E --> F[性能优化]
    ```
    
    **关键活动**：
    - 基于设计稿开发可复用组件
    - 实现页面布局和交互逻辑
    - 集成状态管理方案
    - 对接后端API接口
    - 适配多端设备和浏览器
    
    ### Step 3: 后端开发 (30%)
    ```mermaid
    graph TD
        A[数据模型实现] --> B[业务逻辑开发]
        B --> C[API接口开发]
        C --> D[数据库操作]
        D --> E[安全机制]
        E --> F[性能优化]
    ```
    
    **关键活动**：
    - 实现数据模型和ORM映射
    - 开发核心业务逻辑
    - 实现RESTful API接口
    - 优化数据库查询性能
    - 实现认证授权和数据验证
    
    ### Step 4: 集成测试与部署 (15%)
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[端到端测试]
        C --> D[性能测试]
        D --> E[部署配置]
        E --> F[监控告警]
    ```
    
    **关键活动**：
    - 编写前后端单元测试
    - 进行API集成测试
    - 执行用户场景端到端测试
    - 进行性能压力测试
    - 配置生产环境部署
    
    ### Step 5: 文档与维护 (5%)
    ```mermaid
    graph LR
        A[技术文档] --> B[API文档]
        B --> C[部署文档]
        C --> D[用户手册]
        D --> E[维护计划]
    ```
    
    **关键活动**：
    - 编写技术架构文档
    - 生成API接口文档
    - 编写部署和运维文档
    - 制作用户使用手册
    - 制定后续维护计划
    
    ## 🔄 迭代开发循环
    
    ```mermaid
    graph TD
        A[Sprint规划] --> B[开发执行]
        B --> C[每日同步]
        C --> D[代码审查]
        D --> E[测试验证]
        E --> F[演示反馈]
        F --> G[Sprint回顾]
        G --> A
        
        style A fill:#e1f5fe
        style G fill:#e8f5e9
    ```
    
    **迭代节奏**：
    - **Sprint规划**：确定本轮迭代目标和任务分解
    - **每日同步**：进度同步、问题识别、协作调整
    - **代码审查**：保证代码质量和知识分享
    - **持续集成**：自动化测试和部署流程
    - **演示反馈**：向利益相关者展示成果并收集反馈
  </process>

  <criteria>
    ## 全栈开发质量标准
    
    ### 技术质量指标
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 页面加载时间 ≤ 3秒
    - ✅ API响应时间 ≤ 500ms
    - ✅ 移动端适配完整性 100%
    - ✅ 跨浏览器兼容性 ≥ 95%
    
    ### 用户体验指标
    - ✅ 设计还原度 ≥ 95%
    - ✅ 交互响应及时性
    - ✅ 错误处理友好性
    - ✅ 无障碍访问支持
    - ✅ 用户操作流畅性
    
    ### 项目管理指标
    - ✅ 需求交付及时率 ≥ 90%
    - ✅ 缺陷修复及时率 ≥ 95%
    - ✅ 文档完整性和及时性
    - ✅ 团队协作满意度 ≥ 85%
    - ✅ 技术债务控制在合理范围
  </criteria>
</execution>
