{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-26T15:29:17.252Z", "updatedAt": "2025-07-26T15:29:17.355Z", "resourceCount": 7}, "resources": [{"id": "documentation-driven", "source": "project", "protocol": "execution", "name": "Documentation Driven 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/大牛/execution/documentation-driven.execution.md", "metadata": {"createdAt": "2025-07-26T15:29:17.274Z", "updatedAt": "2025-07-26T15:29:17.274Z", "scannedAt": "2025-07-26T15:29:17.274Z", "path": "role/大牛/execution/documentation-driven.execution.md"}}, {"id": "fullstack-workflow", "source": "project", "protocol": "execution", "name": "Fullstack Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/大牛/execution/fullstack-workflow.execution.md", "metadata": {"createdAt": "2025-07-26T15:29:17.291Z", "updatedAt": "2025-07-26T15:29:17.291Z", "scannedAt": "2025-07-26T15:29:17.291Z", "path": "role/大牛/execution/fullstack-workflow.execution.md"}}, {"id": "fullstack-master", "source": "project", "protocol": "role", "name": "Fullstack Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/fullstack-master/fullstack-master.role.md", "metadata": {"createdAt": "2025-07-26T15:29:17.264Z", "updatedAt": "2025-07-26T15:29:17.264Z", "scannedAt": "2025-07-26T15:29:17.264Z", "path": "role/fullstack-master/fullstack-master.role.md"}}, {"id": "architectural-thinking", "source": "project", "protocol": "thought", "name": "Architectural Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/大牛/thought/architectural-thinking.thought.md", "metadata": {"createdAt": "2025-07-26T15:29:17.303Z", "updatedAt": "2025-07-26T15:29:17.303Z", "scannedAt": "2025-07-26T15:29:17.303Z", "path": "role/大牛/thought/architectural-thinking.thought.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/大牛/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-26T15:29:17.320Z", "updatedAt": "2025-07-26T15:29:17.320Z", "scannedAt": "2025-07-26T15:29:17.320Z", "path": "role/大牛/thought/design-thinking.thought.md"}}, {"id": "project-leadership", "source": "project", "protocol": "thought", "name": "Project Leadership 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/大牛/thought/project-leadership.thought.md", "metadata": {"createdAt": "2025-07-26T15:29:17.337Z", "updatedAt": "2025-07-26T15:29:17.337Z", "scannedAt": "2025-07-26T15:29:17.337Z", "path": "role/大牛/thought/project-leadership.thought.md"}}, {"id": "大牛", "source": "project", "protocol": "role", "name": "大牛 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/大牛/大牛.role.md", "metadata": {"createdAt": "2025-07-26T15:29:17.339Z", "updatedAt": "2025-07-26T15:29:17.339Z", "scannedAt": "2025-07-26T15:29:17.339Z", "path": "role/大牛/大牛.role.md"}}], "stats": {"totalResources": 7, "byProtocol": {"execution": 2, "role": 2, "thought": 3}, "bySource": {"project": 7}}}